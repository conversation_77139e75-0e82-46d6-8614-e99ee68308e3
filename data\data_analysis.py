import pandas as pd
import pymysql

conn = pymysql.connect(
    host='localhost',
    user='root',
    password='123456',
    db='new_energy_analysis'
)
cursor = conn.cursor()

df = pd.read_csv('clean.csv')


def part1():
    # 定义价格区间
    bins = [0, 10, 20, 30, 50, float('inf')]  # 设置价格区间
    labels = ['10万以下', '10万-20万', '20万-30万', '30万-50万', '50万以上']  # 设置区间标签

    # 使用 pd.cut 将最高价格分为不同的区间
    df['price_range'] = pd.cut(df['最高价格'], bins=bins, labels=labels, right=False)

    # 统计每个价格区间的车辆数量
    price_distribution = df['price_range'].value_counts().sort_index()

    # 将结果转为 DataFrame 以便更好查看
    price_distribution_df = price_distribution.reset_index()
    price_distribution_df.columns = ['价格区间', '车辆数量']
    print(price_distribution_df)
    truncate_sql = 'truncate table part1'
    cursor.execute(truncate_sql)
    conn.commit()

    sql = 'insert into part1(name,value) values(%s,%s)'

    for index, row in price_distribution_df.iterrows():
        cursor.execute(sql, (row['价格区间'], row['车辆数量']))
    conn.commit()


def part2():
    df['最高价格'] = df['最高价格'].astype(float)  # 转换为浮点数

    # 按品牌名称分组，计算平均最高价格
    average_price_by_brand = df.groupby('品牌名称')['最高价格'].mean().reset_index()

    # 重命名列
    average_price_by_brand.columns = ['品牌名称', '平均最高价格']
    average_price_by_brand_sort = average_price_by_brand.sort_values(by='平均最高价格', ascending=False).head(10)
    print(average_price_by_brand_sort)
    truncate_sql = 'truncate table part2'
    cursor.execute(truncate_sql)
    conn.commit()

    sql = 'insert into part2(name,value) values(%s,%s)'

    for index, row in average_price_by_brand_sort.iterrows():
        cursor.execute(sql, (row['品牌名称'], row['平均最高价格']))
    conn.commit()


def part3():
    df['销量'] = df['销量'].astype(int)  # 转换为整数
    df['最低价格'] = df['最低价格'].astype(float)  # 转换为浮点数

    # 计算销售额
    df['销售额'] = df['销量'] * df['最低价格']

    # 按品牌名称分组，汇总销售额
    sales_by_brand = df.groupby('品牌名称')[['销量', '销售额']].sum().reset_index()
    print(sales_by_brand)
    sales_by_brand_sort = sales_by_brand.sort_values(by='销售额', ascending=False).head(10)

    truncate_sql = 'truncate table part3'
    cursor.execute(truncate_sql)
    conn.commit()

    sql = 'insert into part3(name,sales,sale_price) values(%s,%s,%s)'

    for index, row in sales_by_brand_sort.iterrows():
        cursor.execute(sql, (row['品牌名称'], row['销量'], row['销售额']))
    conn.commit()


def part4():
    brands_to_filter = ["比亚迪", "小鹏汽车", "蔚来"]
    filtered_df = df[df["品牌名称"].isin(brands_to_filter)]

    # 按时间和品牌名称分组，汇总销量
    result = filtered_df.groupby(["时间", "品牌名称"])["销量"].sum().reset_index()
    print(result)
    truncate_sql = 'truncate table part4'
    cursor.execute(truncate_sql)
    conn.commit()

    sql = 'insert into part4(time,brand,sales) values(%s,%s,%s)'

    for index, row in result.iterrows():
        cursor.execute(sql, (row['时间'], row['品牌名称'], row['销量']))
    conn.commit()


# 上榜次数最多的品牌
def part5():
    brand_counts = df['品牌名称'].value_counts().head(10)
    print(brand_counts)
    truncate_sql = 'truncate table part5'
    cursor.execute(truncate_sql)
    conn.commit()

    sql = 'insert into part5(name,value) values(%s,%s)'

    for index, row in brand_counts.items():
        cursor.execute(sql, (index, row))
    conn.commit()


def part6():
    # 按时间分组，计算总销量
    monthly_sales = df.groupby("时间")["销量"].sum().reset_index()

    # 修改列名为更易读
    monthly_sales.columns = ["时间", "总销量"]
    print(monthly_sales)
    truncate_sql = 'truncate table part6'
    cursor.execute(truncate_sql)
    conn.commit()

    sql = 'insert into part6(name,value) values(%s,%s)'

    for index, row in monthly_sales.iterrows():
        cursor.execute(sql, (row['时间'], row['总销量']))
    conn.commit()


if __name__ == '__main__':
    part1()
    part2()
    part3()
    part4()
    part5()
    part6()
