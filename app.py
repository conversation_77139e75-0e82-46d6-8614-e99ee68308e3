import secrets
import smtplib
import time
from email.header import Header
from email.mime.text import MIMEText
import logging

import pymysql
from flask import Flask, render_template, jsonify, session, request, redirect, url_for

from config import Config

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # 请更改为更安全的密钥
logger = logging.getLogger(__name__)

db_config = {
    'host': Config.Host,
    'user': Config.User,
    'password': Config.Password,
    'port': Config.Port,
    'database': Config.Database,
}

@app.route('/', methods=['GET', 'POST'])
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        try:
            connection = pymysql.connect(**db_config)
            with connection.cursor() as cursor:
                # 查询用户信息
                sql = "SELECT id, password, avatar FROM user_info WHERE username = %s"
                cursor.execute(sql, (username,))
                user = cursor.fetchone()
                if not user:
                    return jsonify({'code': 404, 'message': '用户不存在'})
                if user[1] == password:
                    session['username'] = username
                    session['uid'] = user[0]
                    session['avatar'] = user[2]
                    return jsonify({'code': 200, 'message': '登录成功'})
                else:
                    return jsonify({"code": 500, 'message': '密码错误'})
        except Exception as e:
            return jsonify({'code': 500, 'message': f'登录出错: {str(e)}'})
        finally:
            if connection:
                connection.close()
    return render_template('login.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        email = request.form.get('email')
        phone = request.form.get('phone', '')
        age = request.form.get('age', '')
        avatar = 'static/images/default.jpg'
        # 验证必填字段
        if not all([username, password, email]):
            return jsonify({'code': 400, 'message': '用户名、密码和邮箱不能为空'})
        # 验证邮箱格式
        if not email.endswith('@163.com'):
            return jsonify({'code': 400, 'message': '请使用网易邮箱'})
        try:
            connection = pymysql.connect(**db_config)
            with connection.cursor() as cursor:
                # 检查用户名是否已存在
                sql = "SELECT id FROM user_info WHERE username = %s"
                cursor.execute(sql, (username,))
                if cursor.fetchone():
                    return jsonify({'code': 409, 'message': '用户名已存在'})

                # 检查邮箱是否已注册
                sql = "SELECT id FROM user_info WHERE email = %s"
                cursor.execute(sql, (email,))
                if cursor.fetchone():
                    return jsonify({'code': 409, 'message': '该邮箱已被注册'})
                # 创建新用户
                sql = "INSERT INTO user_info (username, password, email, phone,age, avatar) VALUES (%s, %s, %s, %s, %s, %s)"
                cursor.execute(sql, (username, password, email, phone, age, avatar))
                connection.commit()
                return jsonify({'code': 200, 'message': '注册成功'})
        except Exception as e:
            if connection:
                connection.rollback()
            return jsonify({'code': 500, 'message': f'注册失败: {str(e)}'})
        finally:
            if connection:
                connection.close()
    return render_template('register.html')

@app.route('/send_captcha', methods=['POST'])
def send_captcha():
    """发送验证码邮件接口"""
    if request.method != 'POST':
        return jsonify({'code': 400, 'message': '非法请求方式'})

    connection = None
    try:
        # 获取前端输入
        data = request.get_json()
        username = data.get('username')
        sender_email = Config.Sender_email
        sender_password = Config.Sender_password

        # 验证必填字段
        if not all([username, sender_email, sender_password]):
            return jsonify({'code': 400, 'message': '请填写完整信息'})

        # 连接数据库查询用户
        connection = pymysql.connect(**db_config)
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            sql = "SELECT email FROM user_info WHERE username = %s"
            cursor.execute(sql, (username,))
            user = cursor.fetchone()

            if not user:
                return jsonify({'code': 400, 'message': '用户不存在'})

            recipient_email = user['email']  # 使用email字段

            # 生成6位数字验证码
            captcha = ''.join(secrets.choice('0123456789') for _ in range(6))

            # 配置邮件内容
            msg = MIMEText(
                f"""<html>
                    <body>
                        <h3>密码重置验证码</h3>
                        <p>用户名: {username}</p>
                        <p>验证码: <strong style="color: #1890ff;">{captcha}</strong></p>
                        <p>有效期10分钟，请尽快使用</p>
                    </body>
                </html>""",
                'html', 'utf-8'
            )
            msg['Subject'] = Header('密码重置验证码', 'utf-8')
            msg['From'] = sender_email
            msg['To'] = recipient_email

            # 发送邮件
            try:
                with smtplib.SMTP_SSL('smtp.163.com', 465) as server:
                    server.login(sender_email, sender_password)
                    server.sendmail(sender_email, [recipient_email], msg.as_string())
                # 存储验证信息到session，设置10分钟过期
                session['reset_captcha'] = captcha
                session['reset_user'] = username
                session['reset_time'] = int(time.time())  # 记录发送时间
                session.permanent = True

                return jsonify({
                    'code': 200,
                    'message': '验证码已发送到您的注册邮箱'
                })

            except smtplib.SMTPAuthenticationError:
                return jsonify({'code': 400, 'message': '邮箱账号或密码错误'})
            except smtplib.SMTPException as e:
                logger.error(f"邮件发送失败: {str(e)}")
                return jsonify({'code': 500, 'message': '邮件发送失败，请稍后重试'})

    except Exception as e:
        logger.error(f"系统错误: {str(e)}")
        return jsonify({'code': 500, 'message': '系统错误'})
    finally:
        if connection:
            connection.close()

@app.route('/reset_password', methods=['GET', 'POST'])
def reset_password():
    """重置密码接口"""
    if request.method == 'GET':
        return render_template('reset_password.html')

    # 确保请求是JSON格式
    if not request.is_json:
        return jsonify({'code': 400, 'message': '请求必须是JSON格式'}), 400

    connection = None
    try:
        data = request.get_json()

        # 调试输出接收到的数据
        print("Received reset password data:", data)

        username = data.get('username')
        captcha = data.get('captcha')
        new_password = data.get('password')
        # 验证必填字段
        if not all([username, captcha, new_password]):
            return jsonify({'code': 400, 'message': '缺少必要参数'}), 400

        # 验证验证码是否过期（10分钟有效期）
        if 'reset_time' not in session or int(time.time()) - session['reset_time'] > 600:
            return jsonify({'code': 400, 'message': '验证码已过期，请重新获取'}), 400

        # 验证验证码
        if (captcha != session.get('reset_captcha') or
                username != session.get('reset_user')):
            return jsonify({'code': 400, 'message': '验证码错误'}), 400

        # 验证用户信息
        connection = pymysql.connect(**db_config)
        with connection.cursor() as cursor:
            sql = "SELECT id FROM user_info WHERE username = %s"
            cursor.execute(sql, (username,))
            if not cursor.fetchone():
                return jsonify({'code': 400, 'message': '用户不存在'}), 400

            sql = "UPDATE user_info SET password = %s WHERE username = %s"
            cursor.execute(sql, (new_password, username))
            connection.commit()

        # 清除session中的验证信息
        session.pop('reset_captcha', None)
        session.pop('reset_user', None)
        session.pop('reset_time', None)
        session.pop('sender_email', None)
        session.pop('sender_password', None)

        return jsonify({'code': 200, 'message': '密码重置成功'})

    except Exception as e:
        logger.error(f"密码重置出错: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({'code': 500, 'message': '系统错误'}), 500
    finally:
        if connection:
            connection.close()

@app.route('/index')
def index():
    username = session.get('username')
    avatar = session.get('avatar')
    if not username:
        return redirect(url_for('login'))
    return render_template('index.html', username=username, avatar=avatar)

@app.route('/data_list')
def data_list():
    username = session.get('username')
    avatar = session.get('avatar')
    if not username:
        return redirect(url_for('login'))
    return render_template('data_list.html', username=username, avatar=avatar)

@app.route('/info')
def info():
    username = session.get('username')
    avatar = session.get('avatar')
    if not username:
        return redirect(url_for('login'))
    return render_template('info.html', username=username, avatar=avatar)

@app.route('/vehicle_detail/<int:vehicle_id>')
def vehicle_detail(vehicle_id):
    username = session.get('username')
    avatar = session.get('avatar')
    if not username:
        return redirect(url_for('login'))

    connection = None
    try:
        connection = pymysql.connect(**db_config)
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 获取车辆详细信息
            sql = """SELECT * FROM vehicle_data WHERE id = %s"""
            cursor.execute(sql, (vehicle_id,))
            vehicle = cursor.fetchone()

            if not vehicle:
                return "车辆不存在", 404

            return render_template('vehicle_detail.html', vehicle=vehicle, username=username, avatar=avatar)
    except Exception as e:
        logger.error(f"获取车辆详情失败: {str(e)}")
        return "服务器错误", 500
    finally:
        if connection:
            connection.close()

# API路由
@app.route('/api/brands')
def api_brands():
    """获取品牌列表"""
    connection = None
    try:
        connection = pymysql.connect(**db_config)
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            sql = "SELECT DISTINCT brandname FROM vehicle_data ORDER BY brandname"
            cursor.execute(sql)
            brands = cursor.fetchall()
            return jsonify({'code': 200, 'brands': brands})
    except Exception as e:
        logger.error(f"获取品牌列表失败: {str(e)}")
        return jsonify({'code': 500, 'message': '获取品牌列表失败'})
    finally:
        if connection:
            connection.close()

@app.route('/api/vehicles')
def api_vehicles():
    """获取车辆列表"""
    page = int(request.args.get('page', 1))
    per_page = 12
    offset = (page - 1) * per_page

    # 获取筛选条件
    brand = request.args.get('brand', '')
    carname = request.args.get('carname', '')
    pricerange = request.args.get('pricerange', '')
    time_filter = request.args.get('time', '')
    sort = request.args.get('sort', '')

    connection = None
    try:
        connection = pymysql.connect(**db_config)
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 构建查询条件
            where_conditions = []
            params = []

            if brand:
                where_conditions.append("brandname = %s")
                params.append(brand)

            if carname:
                where_conditions.append("carname LIKE %s")
                params.append(f"%{carname}%")

            if pricerange:
                if pricerange == '0-5':
                    where_conditions.append("lowestprice < 5")
                elif pricerange == '5-10':
                    where_conditions.append("lowestprice >= 5 AND lowestprice < 10")
                elif pricerange == '10-20':
                    where_conditions.append("lowestprice >= 10 AND lowestprice < 20")
                elif pricerange == '20-30':
                    where_conditions.append("lowestprice >= 20 AND lowestprice < 30")
                elif pricerange == '30-50':
                    where_conditions.append("lowestprice >= 30 AND lowestprice < 50")
                elif pricerange == '50-100':
                    where_conditions.append("lowestprice >= 50 AND lowestprice < 100")
                elif pricerange == '100-999':
                    where_conditions.append("lowestprice >= 100")

            if time_filter:
                where_conditions.append("time = %s")
                params.append(time_filter)

            where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

            # 构建排序条件
            order_clause = ""
            if sort == 'sales':
                order_clause = " ORDER BY sales DESC"
            elif sort == 'price':
                order_clause = " ORDER BY lowestprice ASC"
            elif sort == 'rank':
                order_clause = " ORDER BY rank ASC"
            else:
                order_clause = " ORDER BY rank ASC"

            # 获取总数
            count_sql = f"SELECT COUNT(*) as total FROM vehicle_data{where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']

            # 获取车辆数据
            sql = f"""SELECT * FROM vehicle_data{where_clause}{order_clause} LIMIT %s OFFSET %s"""
            cursor.execute(sql, params + [per_page, offset])
            vehicles = cursor.fetchall()

            # 计算分页信息
            total_pages = (total + per_page - 1) // per_page

            return jsonify({
                'code': 200,
                'vehicles': vehicles,
                'pagination': {
                    'current_page': page,
                    'total_pages': total_pages,
                    'total_items': total,
                    'per_page': per_page
                }
            })
    except Exception as e:
        logger.error(f"获取车辆列表失败: {str(e)}")
        return jsonify({'code': 500, 'message': '获取车辆列表失败'})
    finally:
        if connection:
            connection.close()

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

if __name__ == '__main__':
    app.run(debug=True)
