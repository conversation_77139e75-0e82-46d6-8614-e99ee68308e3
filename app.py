import pymysql
from flask import Flask, render_template, jsonify, session, request

from config import Config

app = Flask(__name__)

db_config = {
    'host': Config.Host,
    'user': Config.User,
    'password': Config.Password,
    'port': Config.Port,
    'database': Config.Database,
}

@app.route('/', methods=['GET', 'POST'])
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        try:
            connection = pymysql.connect(**db_config)
            with connection.cursor() as cursor:
                # 查询用户信息
                sql = "SELECT id, password, avatar FROM user_info WHERE username = %s"
                cursor.execute(sql, (username,))
                user = cursor.fetchone()
                if not user:
                    return jsonify({'code': 404, 'message': '用户不存在'})
                if user[1] == password:
                    session['username'] = username
                    session['uid'] = user[0]
                    session['avatar'] = user[2]
                    return jsonify({'code': 200, 'message': '登录成功'})
                else:
                    return jsonify({"code": 500, 'message': '密码错误'})
        except Exception as e:
            return jsonify({'code': 500, 'message': f'登录出错: {str(e)}'})
        finally:
            if connection:
                connection.close()
    return render_template('login.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        email = request.form.get('email')
        phone = request.form.get('phone', '')
        age = request.form.get('age', '')
        avatar = 'static/images/default.jpg'
        # 验证必填字段
        if not all([username, password, email]):
            return jsonify({'code': 400, 'message': '用户名、密码和邮箱不能为空'})
        # 验证邮箱格式
        if not email.endswith('@163.com'):
            return jsonify({'code': 400, 'message': '请使用网易邮箱'})
        try:
            connection = pymysql.connect(**db_config)
            with connection.cursor() as cursor:
                # 检查用户名是否已存在
                sql = "SELECT id FROM user_info WHERE username = %s"
                cursor.execute(sql, (username,))
                if cursor.fetchone():
                    return jsonify({'code': 409, 'message': '用户名已存在'})

                # 检查邮箱是否已注册
                sql = "SELECT id FROM user_info WHERE email = %s"
                cursor.execute(sql, (email,))
                if cursor.fetchone():
                    return jsonify({'code': 409, 'message': '该邮箱已被注册'})
                # 创建新用户
                sql = "INSERT INTO user_info (username, password, email, phone,age, avatar) VALUES (%s, %s, %s, %s, %s, %s)"
                cursor.execute(sql, (username, password, email, phone, age, avatar))
                connection.commit()
                return jsonify({'code': 200, 'message': '注册成功'})
        except Exception as e:
            if connection:
                connection.rollback()
            return jsonify({'code': 500, 'message': f'注册失败: {str(e)}'})
        finally:
            if connection:
                connection.close()
    return render_template('register.html')

if __name__ == '__main__':
    app.run()
