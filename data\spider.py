import csv
import requests
def spider(page, month):
    headers = {
        "user-agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36"
    }
    url = f'https://www.dongchedi.com/motor/pc/car/rank_data?aid=1839&app_name=auto_web_pc&city_name=%E4%B8%8A%E6%B5%B7&count=10&offset={page}&month={month}&new_energy_type=1%2C2%2C3&rank_data_type=11&brand_id=&price=&manufacturer=&outter_detail_type=&nation=0'
    resp = requests.get(url, headers=headers).json()
    feeds = resp['data']['list']
    with open('data.csv', 'a', newline='', encoding='utf-8') as csvfile:
        csvwriter = csv.writer(csvfile)
        rows = []
        for feed in feeds:
            series_id = feed['series_id']  # 编号
            series_name = feed['series_name']  # 车名
            image = feed['image']  # 图片
            rank = feed['rank']  # 排行
            min_price = feed['min_price']  # 最低价格
            max_price = feed['max_price']  # 最高价格
            last_rank = feed['last_rank']  # 上月排名
            count = feed['count']  # 销量
            score = feed['score']  # 评分
            car_review_count = feed['car_review_count']  # 评论数
            descender_price = feed['descender_price']  # 价格下降幅度
            series_pic_count = feed['series_pic_count']  # 相关的图片
            brand_id = feed['brand_id']  # 品牌id
            outter_detail_type = feed['outter_detail_type']  # 详细信息类型
            brand_name = feed['brand_name']  # 品牌名称
            sub_brand_id = feed['sub_brand_id']  # 子品牌id
            sub_brand_name = feed['sub_brand_name']  # 子品牌名称
            price = feed['price']  # 价格区间
            dealer_price = feed['dealer_price']  # 经销商报价
            month = month
            rows.append(
                [series_id, series_name, image, rank, min_price, max_price, last_rank, count, score, car_review_count,
                 descender_price, series_pic_count, brand_id, outter_detail_type, brand_name, sub_brand_id,
                 sub_brand_name, price, dealer_price, month])
        csvwriter.writerows(rows)


# month_list = ['202301', '202302', '202303', '202304', '202305', '202306', '202307', '202308', '202309', '202310',
#               '202311', '202312', '202401', '202402', '202403', '202404', '202405', '202406', '202407',
#               '202408', '202409']
month_list = [ '202412', '202501', '202502']
for month in month_list:
    for page in range(1, 30):
        page = page * 10
        spider(page,month)
        print(f'{month}月爬取中.....')
