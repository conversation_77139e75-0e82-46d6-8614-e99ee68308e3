import pandas as pd
from sqlalchemy import create_engine

df = pd.read_csv('clean.csv')

columns = ['vehicleid', 'carname', 'image', 'rank', 'lowestprice', 'highestprice',
           'lastmonthrank', 'sales', 'rating', 'commentcount', 'pricedrop',
           'relatedimages', 'brandid', 'detailtype', 'brandname',
           'subbrandid', 'subbrandname', 'pricerange', 'dealerprice', 'time']

engine = create_engine('mysql+pymysql://root:123456@localhost:3306/new_energy_analysis?charset=utf8')

df.columns = columns

df.to_sql('data', con=engine, index=False,if_exists='replace')
