{% extends 'base.html' %}

{% block content %}
    <div class="pcoded-main-container">
        <div class="pcoded-content">
            <div class="page-header">
                <div class="page-block">
                    <div class="row align-items-center">
                        <div class="col-md-12">
                            <div class="page-header-title">
                                <h5 class="m-b-10">控制面板</h5>
                            </div>
                            <ul class="breadcrumb">
                                <li class="breadcrumb-item"><a href="index.html"><i class="feather icon-home"></i></a>
                                </li>
                                <li class="breadcrumb-item"><a href="#!">控制面板</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-7 col-md-12">

                    <div class="row">
                        <div class="col-sm-6">
                            <div class="card support-bar overflow-hidden">
                                <div class="card-body pb-0">
                                    <h2 class="m-0">{{ result.brand }}</h2>
                                    <span class="text-c-blue">汽车销量趋势</span>
                                </div>
                                <div id="date-chart" style="height: 240px;width: 100%"></div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="card support-bar overflow-hidden">
                                <div class="card-body pb-0">
                                    <h2 class="m-0">{{ result.brand }} 不同车型销量占比</h2>
                                    <span class="text-c-green">该品牌下不同车型销量占比</span>
                                </div>
                                <div id="sub_brand-chart" style="height: 240px;width: 100%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-5 col-md-12">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-8">
                                            <h4 class="text-c-yellow">${{ result.max_price }}万</h4>
                                            <h6 class="text-muted m-b-0">最高价格</h6>
                                        </div>
                                        <div class="col-4 text-right">
                                            <i class="feather icon-tag f-28"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer bg-c-yellow">
                                    <div class="row align-items-center">
                                        <div class="col-9">
                                            <p class="text-white m-b-0">行业最高</p>
                                        </div>
                                        <div class="col-3 text-right">
                                            <i class="feather icon-arrow-up text-white f-16"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 汽车总数卡片 -->
                        <div class="col-sm-6">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-8">
                                            <h4 class="text-c-green">{{ result.total_cars }}</h4>
                                            <h6 class="text-muted m-b-0">汽车总数</h6>
                                        </div>
                                        <div class="col-4 text-right">
                                            <i class="feather icon-database f-28"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer bg-c-green">
                                    <div class="row align-items-center">
                                        <div class="col-9">
                                            <p class="text-white m-b-0">车型数量</p>
                                        </div>
                                        <div class="col-3 text-right">
                                            <i class="feather icon-activity text-white f-16"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-6">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-8">
                                            <h4 class="text-c-red">{{ result.brands }}</h4>
                                            <h6 class="text-muted m-b-0">品牌数量</h6>
                                        </div>
                                        <div class="col-4 text-right">
                                            <i class="feather icon-award f-28"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer bg-c-red">
                                    <div class="row align-items-center">
                                        <div class="col-9">
                                            <p class="text-white m-b-0">品牌统计</p>
                                        </div>
                                        <div class="col-3 text-right">
                                            <i class="feather icon-trending-up text-white f-16"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-8">
                                            <h4 class="text-c-blue">{{ result.car_name }}</h4>
                                            <h6 class="text-muted m-b-0">销量最高</h6>
                                        </div>
                                        <div class="col-4 text-right">
                                            <i class="feather icon-star f-28"></i>  <!-- 更新为星星图标 -->
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer bg-c-blue">
                                    <div class="row align-items-center">
                                        <div class="col-9">
                                            <p class="text-white m-b-0">热销车型</p>  <!-- 更新描述文字 -->
                                        </div>
                                        <div class="col-3 text-right">
                                            <i class="feather icon-award text-white f-16"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-6 col-md-12">
                    <div class="card table-card">
                        <div class="card-header">
                            <h5>热销车型TOP5</h5>
                            <div class="card-header-right">
                                <div class="btn-group card-option">
                                    <button type="button" class="btn dropdown-toggle" data-toggle="dropdown"
                                            aria-haspopup="true" aria-expanded="false">
                                        <i class="feather icon-more-horizontal"></i>
                                    </button>
                                    <ul class="list-unstyled card-option dropdown-menu dropdown-menu-right">
                                        <li class="dropdown-item full-card"><a href="#!"><span><i
                                                class="feather icon-maximize"></i> maximize</span><span
                                                style="display:none"><i
                                                class="feather icon-minimize"></i> Restore</span></a></li>
                                        <li class="dropdown-item minimize-card"><a href="#!"><span><i
                                                class="feather icon-minus"></i> collapse</span><span
                                                style="display:none"><i
                                                class="feather icon-plus"></i> expand</span></a></li>
                                        <li class="dropdown-item reload-card"><a href="#!"><i
                                                class="feather icon-refresh-cw"></i> reload</a></li>
                                        <li class="dropdown-item close-card"><a href="#!"><i
                                                class="feather icon-trash"></i> remove</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>车型图片</th>
                                        <th>车名</th>
                                        <th>品牌</th>
                                        <th>销量</th>
                                        <th class="text-right">价格区间(万)</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {% for car in result.top5_result.itertuples() %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>
                                                <div class="d-inline-block align-middle">
                                                    <img src="{{ car.图片 }}" alt="{{ car.车名 }}"
                                                         class="img-radius wid-40 align-top m-r-15"
                                                         style="width:60px;height:auto;">
                                                </div>
                                            </td>
                                            <td>{{ car.车名 }}</td>
                                            <td>{{ car.品牌名称 }}</td>
                                            <td>{{ car.销量 }}</td>
                                            <td class="text-right">
                                                <label class="badge badge-light-primary">
                                                    {{ car.最低价格 }}-{{ car.最高价格 }}
                                                </label>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-6 col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5>热销车型大图展示</h5>
                        </div>
                        <div class="card-body" style="height: 430px">
                            <div id="top5-carousel" class="carousel slide" data-ride="carousel">
                                <!-- 轮播指示器 -->
                                <ol class="carousel-indicators">
                                    {% for i in range(result.top5_result|length) %}
                                        <li data-target="#top5-carousel" data-slide-to="{{ i }}"
                                            {% if loop.first %}class="active"{% endif %}></li>
                                    {% endfor %}
                                </ol>

                                <!-- 轮播内容 -->
                                <div class="carousel-inner" style="border-radius: 10px;">
                                    {% for car in result.top5_result.itertuples() %}
                                        <div class="carousel-item {% if loop.first %}active{% endif %}">
                                            <img src="{{ car.图片 }}" class="d-block w-100" alt="{{ car.车名 }}"
                                                 style="height: 350px; object-fit: cover;background-color: #b9bbbe">
                                            <!-- 悬停显示的信息层 -->
                                            <div class="carousel-caption d-none d-md-block"
                                                 style="background: rgba(0,0,0,0.5); border-radius: 20px;margin-bottom: 100px;width: 300px;margin-left: 260px">
                                                <h4 style="color: white">{{ car.车名 }}</h4>
                                                <p>销量: {{ car.销量 }} | 价格: {{ car.最低价格 }}-{{ car.最高价格 }}万</p>
                                                <a href="#" class="btn btn-sm btn-primary">查看详情</a>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>

                                <!-- 左右控制按钮 -->
                                <a class="carousel-control-prev" href="#top5-carousel" role="button" data-slide="prev">
                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                    <span class="sr-only">Previous</span>
                                </a>
                                <a class="carousel-control-next" href="#top5-carousel" role="button" data-slide="next">
                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                    <span class="sr-only">Next</span>
                                </a>
                            </div>

                            <!-- 车型信息表格（移动端备用） -->
                            <div class="table-responsive d-md-none mt-3">
                                <table class="table table-sm">
                                    <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>车名</th>
                                        <th>销量</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {% for car in result.top5_result.itertuples() %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ car.车名 }}</td>
                                            <td>{{ car.销量 }}</td>
                                        </tr>
                                    {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <style>
                    /* 自定义轮播图样式 */
                    #top5-carousel {
                        position: relative;
                    }

                    .carousel-item {
                        transition: transform 0.6s ease-in-out;
                    }

                    .carousel-caption {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        padding: 15px;
                        opacity: 0;
                        transition: opacity 0.3s ease;
                    }

                    .carousel-item:hover .carousel-caption {
                        opacity: 1;
                    }

                    .carousel-control-prev, .carousel-control-next {
                        width: 5%;
                    }

                    .carousel-indicators li {
                        width: 10px;
                        height: 10px;
                        border-radius: 50%;
                    }
                </style>


            </div>

        </div>
    </div>
    <script src="{{ url_for('static', filename='js/echarts.js') }}"></script>
    <script>
        // 自动轮播设置
        $(document).ready(function () {
            $('#top5-carousel').carousel({
                interval: 3000,  // 3秒切换一次
                pause: "hover"   // 鼠标悬停时暂停
            });
        });
    </script>
    <script>
        var chartDom = document.getElementById('date-chart');
        var myChart = echarts.init(chartDom);
        var option;

        option = {
            tooltip: {
                trigger: 'axis',
                formatter: '{b}月<br/>销量: {c}辆'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: {{ result.date_list|tojson }},
                axisLine: {
                    lineStyle: {
                        color: '#95a5a6'
                    }
                },
                axisLabel: {
                    color: '#7f8c8d'
                }
            },
            yAxis: {
                type: 'value',
                name: '销量(辆)',
                nameTextStyle: {
                    color: '#7f8c8d',
                    padding: [0, 0, 0, 40]
                },
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#95a5a6'
                    }
                },
                splitLine: {
                    lineStyle: {
                        color: '#ecf0f1',
                        type: 'dashed'
                    }
                },
                axisLabel: {
                    color: '#7f8c8d'
                }
            },
            series: [
                {
                    name: '销量',
                    data: {{ result.sales_list|tojson }},
                    type: 'line',
                    smooth: true,
                    symbol: 'circle',
                    symbolSize: 8,
                    lineStyle: {
                        width: 3,
                        color: '#3498db'
                    },
                    itemStyle: {
                        color: '#3498db',
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {offset: 0, color: 'rgba(52, 152, 219, 0.5)'},
                            {offset: 1, color: 'rgba(52, 152, 219, 0.1)'}
                        ])
                    }
                }
            ]
        };

        option && myChart.setOption(option);
    </script>
    <script>
        var chartDom = document.getElementById('sub_brand-chart');
        var myChart = echarts.init(chartDom);
        var option;

        option = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            series: [
                {
                    name: '销量占比',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: true,
                        formatter: '{b}: {d}%',
                        color: '#2c3e50'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: true
                    },
                    data: {{ result.data_list|tojson }},
                    color: ['#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6',
                        '#1abc9c', '#d35400', '#34495e', '#7f8c8d', '#27ae60']
                }
            ]
        };

        option && myChart.setOption(option);
    </script>
{% endblock %}