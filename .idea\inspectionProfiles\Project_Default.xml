<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="52">
            <item index="0" class="java.lang.String" itemvalue="decorator" />
            <item index="1" class="java.lang.String" itemvalue="tzlocal" />
            <item index="2" class="java.lang.String" itemvalue="alembic" />
            <item index="3" class="java.lang.String" itemvalue="greenlet" />
            <item index="4" class="java.lang.String" itemvalue="validators" />
            <item index="5" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="6" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="7" class="java.lang.String" itemvalue="cffi" />
            <item index="8" class="java.lang.String" itemvalue="marshmallow" />
            <item index="9" class="java.lang.String" itemvalue="update" />
            <item index="10" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="11" class="java.lang.String" itemvalue="pycparser" />
            <item index="12" class="java.lang.String" itemvalue="importlib-metadata" />
            <item index="13" class="java.lang.String" itemvalue="Jinja2" />
            <item index="14" class="java.lang.String" itemvalue="Flask-Login" />
            <item index="15" class="java.lang.String" itemvalue="flask_wtf" />
            <item index="16" class="java.lang.String" itemvalue="zipp" />
            <item index="17" class="java.lang.String" itemvalue="itsdangerous" />
            <item index="18" class="java.lang.String" itemvalue="Flask" />
            <item index="19" class="java.lang.String" itemvalue="Flask-Session" />
            <item index="20" class="java.lang.String" itemvalue="blinker" />
            <item index="21" class="java.lang.String" itemvalue="marshmallow-sqlalchemy" />
            <item index="22" class="java.lang.String" itemvalue="six" />
            <item index="23" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="24" class="java.lang.String" itemvalue="Flask-Reuploaded" />
            <item index="25" class="java.lang.String" itemvalue="tzdata" />
            <item index="26" class="java.lang.String" itemvalue="cryptography" />
            <item index="27" class="java.lang.String" itemvalue="packaging" />
            <item index="28" class="java.lang.String" itemvalue="click" />
            <item index="29" class="java.lang.String" itemvalue="Flask-SQLAlchemy" />
            <item index="30" class="java.lang.String" itemvalue="APScheduler" />
            <item index="31" class="java.lang.String" itemvalue="psutil" />
            <item index="32" class="java.lang.String" itemvalue="pandas" />
            <item index="33" class="java.lang.String" itemvalue="Flask-APScheduler" />
            <item index="34" class="java.lang.String" itemvalue="flask-marshmallow" />
            <item index="35" class="java.lang.String" itemvalue="colorama" />
            <item index="36" class="java.lang.String" itemvalue="Flask-Mail" />
            <item index="37" class="java.lang.String" itemvalue="Flask-Migrate" />
            <item index="38" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="39" class="java.lang.String" itemvalue="style" />
            <item index="40" class="java.lang.String" itemvalue="PyMySQL" />
            <item index="41" class="java.lang.String" itemvalue="pytz" />
            <item index="42" class="java.lang.String" itemvalue="wtforms" />
            <item index="43" class="java.lang.String" itemvalue="Mako" />
            <item index="44" class="java.lang.String" itemvalue="Pillow" />
            <item index="45" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="46" class="java.lang.String" itemvalue="matplotlib" />
            <item index="47" class="java.lang.String" itemvalue="numpy" />
            <item index="48" class="java.lang.String" itemvalue="xgboost" />
            <item index="49" class="java.lang.String" itemvalue="django-simpleui" />
            <item index="50" class="java.lang.String" itemvalue="Django" />
            <item index="51" class="java.lang.String" itemvalue="certifi" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>