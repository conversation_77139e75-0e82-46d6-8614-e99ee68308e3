import pandas as pd
import matplotlib.pyplot as plt
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from sklearn.metrics import mean_squared_error, mean_absolute_error
import matplotlib
matplotlib.use('Tkagg')
from matplotlib import rcParams

# 设置中文字体
rcParams['font.family'] = 'SimHei'

# 数据预处理
df = pd.read_csv('clean.csv')
df['时间'] = pd.to_datetime(df['时间'], format='%Y%m')

# 筛选比亚迪数据并按时间排序
byd_df = df[df['品牌名称'] == '比亚迪'].copy()

# 按月份聚合总销量
monthly_sales = byd_df.groupby(pd.Grouper(key='时间', freq='M'))['销量'].sum()
monthly_sales = monthly_sales.asfreq('M').ffill()

# 假设实际销量也是monthly_sales的最后5个月
actual_sales = monthly_sales[-5:]  # 获取最新5个月的实际销量数据

# 可视化时间序列
plt.figure(figsize=(12, 6))
monthly_sales.plot(title='比亚迪月度销量趋势')
plt.xlabel('日期')
plt.ylabel('销量')
plt.show()

# 差分处理（使序列平稳）
sales_diff = monthly_sales.diff().dropna()

# ACF和PACF图
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
plot_acf(sales_diff, ax=ax1)
plot_pacf(sales_diff, ax=ax2)
plt.show()

# 建立ARIMA模型
model = ARIMA(monthly_sales, order=(1, 1, 1))
model_fit = model.fit()
print("ARIMA模型的评估指标：")
print(model_fit.summary())

# 进行未来5个月的ARIMA预测
forecast = model_fit.get_forecast(steps=5)
forecast_index = pd.date_range(
    start=monthly_sales.index[-1] + pd.DateOffset(months=1),
    periods=5,
    freq='M'
)

# 可视化ARIMA预测结果
plt.figure(figsize=(12, 6))
monthly_sales.plot(label='历史数据')
forecast.predicted_mean.plot(label='ARIMA预测值', style='--')
plt.fill_between(
    forecast_index,
    forecast.conf_int().iloc[:, 0],
    forecast.conf_int().iloc[:, 1],
    color='gray',
    alpha=0.2
)
plt.title('比亚迪销量预测（ARIMA模型）')
plt.xlabel('日期')
plt.ylabel('销量')
plt.legend()
plt.show()

# 输出预测结果
arima_forecast = forecast.predicted_mean.round().astype(int)
print("未来五个月ARIMA销量预测：")
print(arima_forecast)

# 计算均方误差和平均绝对误差
arima_mse = mean_squared_error(actual_sales, arima_forecast)
arima_mae = mean_absolute_error(actual_sales, arima_forecast)

print("ARIMA模型的均方误差（MSE）：", arima_mse)
print("ARIMA模型的平均绝对误差（MAE）：", arima_mae)

# SARIMA模型预测
# 使用季节性参数（这里以季节长度为12为例）
seasonal_order = (1, 1, 1, 12)  # (P, D, Q, s)

sarima_model = SARIMAX(monthly_sales, order=(1, 1, 1), seasonal_order=seasonal_order)
sarima_fit = sarima_model.fit()

# 输出SARIMA模型的评估指标
print("SARIMA模型的评估指标：")
print(sarima_fit.summary())

# 进行未来5个月的SARIMA预测
sarima_forecast = sarima_fit.get_forecast(steps=5)

# 可视化SARIMA预测结果
plt.figure(figsize=(12, 6))
monthly_sales.plot(label='历史数据')
sarima_forecast.predicted_mean.plot(label='SARIMA预测值', style='--')
plt.fill_between(
    forecast_index,
    sarima_forecast.conf_int().iloc[:, 0],
    sarima_forecast.conf_int().iloc[:, 1],
    color='gray',
    alpha=0.2
)
plt.title('比亚迪销量预测（SARIMA模型）')
plt.xlabel('日期')
plt.ylabel('销量')
plt.legend()
plt.show()

# 输出SARIMA预测结果
sarima_forecast_values = sarima_forecast.predicted_mean.round().astype(int)
print("未来五个月SARIMA销量预测：")
print(sarima_forecast_values)

# 计算均方误差和平均绝对误差
sarima_mse = mean_squared_error(actual_sales, sarima_forecast_values)
sarima_mae = mean_absolute_error(actual_sales, sarima_forecast_values)

print("SARIMA模型的均方误差（MSE）：", sarima_mse)
print("SARIMA模型的平均绝对误差（MAE）：", sarima_mae)
