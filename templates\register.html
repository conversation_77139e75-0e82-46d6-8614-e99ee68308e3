<!DOCTYPE html>
<html lang="en">

<head>

	<title>新能源汽车数据分析系统</title>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="description" content="" />
	<meta name="keywords" content="">
	<meta name="author" content="Phoenixcoded" />
    <link rel="icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>

<div class="auth-wrapper">
	<div class="auth-content">
		<div class="card">
			<div class="row align-items-center text-center">
				<div class="col-md-12">
                    <form action="">
                        <div class="card-body">
						<img src="{{ url_for('static',filename='images/logo-dark.png') }}" alt="" class="img-fluid mb-4">
						<h4 class="mb-3 f-w-400">用户注册</h4>
						<div class="form-group mb-3">
							<label class="floating-label" for="username">用户名</label>
							<input type="text" class="form-control" id="username" placeholder="">
						</div>
						<div class="form-group mb-3">
							<label class="floating-label" for="email">邮箱地址</label>
							<input type="text" class="form-control" id="email" placeholder="">
						</div>
						<div class="form-group mb-4">
							<label class="floating-label" for="password">密码</label>
							<input type="password" class="form-control" id="password" placeholder="">
						</div>
                        <div class="form-group mb-4">
							<label class="floating-label" for="phone">手机号</label>
							<input type="text" class="form-control" id="phone" placeholder="">
						</div>
                        <div class="form-group mb-4">
							<label class="floating-label" for="age">年龄</label>
							<input type="text" class="form-control" id="age" placeholder="">
						</div>
						<div class="custom-control custom-checkbox  text-left mb-4 mt-2">
							<input type="checkbox" class="custom-control-input" id="customCheck1">
							<label class="custom-control-label" for="customCheck1">我同意相关<a href="#!"> 协议与条约</a> .</label>
						</div>
						<button class="btn btn-primary btn-block mb-4" id="registerBtn">注册</button>
						<p class="mb-2">已经有账号了? <a href="{{ url_for('login') }}" class="f-w-400">去登录</a></p>
					</div>
                    </form>

				</div>
			</div>
		</div>
	</div>
</div>

<script src="{{ url_for('static', filename='js/vendor-all.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/plugins/bootstrap.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/ripple.js') }}"></script>
<script src="{{ url_for('static', filename='js/pcoded.min.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const registerBtn = document.getElementById('registerBtn');
    const usernameInput = document.getElementById('username');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const phoneInput = document.getElementById('phone');
    const ageInput = document.getElementById('age');
    const agreeCheckbox = document.getElementById('customCheck1');

    registerBtn.addEventListener('click', function(e) {
        e.preventDefault();

        const username = usernameInput.value.trim();
        const email = emailInput.value.trim();
        const password = passwordInput.value.trim();
        const phone = phoneInput.value.trim();
        const age = ageInput.value.trim();

        // 基本验证
        if (!username) {
            alert('请输入用户名');
            usernameInput.focus();
            return;
        }

        if (!email) {
            alert('请输入邮箱地址');
            emailInput.focus();
            return;
        }

        // 验证邮箱格式
        if (!email.endsWith('@163.com')) {
            alert('请使用网易邮箱（@163.com）');
            emailInput.focus();
            return;
        }

        if (!password) {
            alert('请输入密码');
            passwordInput.focus();
            return;
        }

        if (password.length < 6) {
            alert('密码长度至少6位');
            passwordInput.focus();
            return;
        }

        if (!agreeCheckbox.checked) {
            alert('请同意相关协议与条约');
            return;
        }

        // 禁用按钮防止重复提交
        registerBtn.disabled = true;
        registerBtn.textContent = '注册中...';

        // 创建表单数据
        const formData = new FormData();
        formData.append('username', username);
        formData.append('email', email);
        formData.append('password', password);
        formData.append('phone', phone);
        formData.append('age', age);

        // 发送注册请求
        fetch('/register', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                alert('注册成功！请登录');
                // 注册成功后跳转到登录页面
                window.location.href = '/login';
            } else {
                alert(data.message || '注册失败');
            }
        })
        .catch(error => {
            console.error('注册请求失败:', error);
            alert('网络错误，请稍后重试');
        })
        .finally(() => {
            // 恢复按钮状态
            registerBtn.disabled = false;
            registerBtn.textContent = '注册';
        });
    });

    // 回车键注册
    [usernameInput, emailInput, passwordInput, phoneInput, ageInput].forEach(input => {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                registerBtn.click();
            }
        });
    });

    // 实时验证邮箱格式
    emailInput.addEventListener('blur', function() {
        const email = this.value.trim();
        if (email && !email.endsWith('@163.com')) {
            this.style.borderColor = '#dc3545';
            // 可以在这里添加错误提示样式
        } else {
            this.style.borderColor = '';
        }
    });
});
</script>



</body>

</html>
