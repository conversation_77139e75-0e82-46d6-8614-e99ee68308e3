<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>找回密码 - 商品数据分析系统</title>
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2ecc71;
            --dark-color: #2c3e50;
            --light-color: #ecf0f1;
            --danger-color: #e74c3c;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            overflow-x: hidden;
        }

        .header-title {
            padding: 80px 0 40px;
            text-align: center;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            margin-bottom: 50px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .header-title h1 {
            font-weight: 700;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
        }

        .panel-login {
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            padding: 30px;
            margin-top: 20px;
        }

        .password-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--dark-color);
            position: relative;
            padding-bottom: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .password-title:after {
            content: '';
            position: absolute;
            width: 50px;
            height: 3px;
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 3px;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-control {
            height: 50px;
            border-radius: 8px;
            padding: 10px 15px;
            border: 1px solid #ddd;
            transition: all 0.3s;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .btn-login {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            padding: 12px;
            font-weight: 600;
            letter-spacing: 1px;
            transition: all 0.3s;
            border-radius: 8px;
            width: 100%;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .alert-message {
            display: none;
            padding: 12px 15px;
            margin-bottom: 20px;
            border-radius: 8px;
        }

        .alert-success {
            background-color: rgba(46, 204, 113, 0.2);
            color: #27ae60;
            border-left: 4px solid var(--secondary-color);
        }

        .alert-error {
            background-color: rgba(231, 76, 60, 0.2);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        .form-note {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .resend-code {
            margin-top: 10px;
            text-align: right;
        }

        .resend-code a {
            color: var(--primary-color);
            cursor: pointer;
            transition: all 0.3s;
        }

        .resend-code a:hover {
            color: var(--secondary-color);
            text-decoration: none;
        }

        .countdown {
            color: #666;
            font-size: 12px;
        }

        .back-to-login {
            text-align: center;
            margin-top: 20px;
        }

        .back-to-login a {
            color: var(--primary-color);
            transition: all 0.3s;
        }

        .back-to-login a:hover {
            color: var(--secondary-color);
            text-decoration: none;
        }

        @media (max-width: 768px) {
            .header-title {
                padding: 50px 0 30px;
            }

            .panel-login {
                padding: 20px;
            }
        }
        .popup-bg {
            background: url({{ url_for('static',filename='images/slider-item-3.jpg') }}) no-repeat center;
            position: fixed;
            width: 100vw;
            height: 100vh;
            top: 0;
            left: 0;
            z-index: -1;
            opacity: 0.6;
            background-size: cover;
            filter: brightness(0.8) blur(2px);
        }
    </style>
</head>

<body>
<div class="popup-bg"></div>
<div class="header-title">
    <div class="container">
        <h1 class="text-white">找回密码</h1>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-md-6 offset-md-3">
            <div class="panel-login">
                <div class="panel-heading">
                    <div class="password-title">找回密码</div>
                </div>
                <div class="panel-body">
                    <div id="alert-message" class="alert-message"></div>

                    <!-- 第一步：输入用户名 -->
                    <div id="step1" class="step active">
                        <form id="request-code-form">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <div class="form-group">
                                <input type="text" name="username" id="username" class="form-control" placeholder="用户名" required>
                            </div>
                            <div class="form-group">
                                <button type="submit" id="request-code-btn" class="btn-login">
                                    获取验证码
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- 第二步：输入验证码和新密码 -->
                    <div id="step2" class="step">
                        <form id="reset-password-form">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <input type="hidden" name="username" id="hidden-username">
                            <div class="form-group">
                                <input type="text" name="captcha" id="captcha" class="form-control" placeholder="验证码" required>
                                <div class="resend-code">
                                    <span class="countdown" id="countdown">60秒后可重新发送</span>
                                    <a id="resend-code">重新发送验证码</a>
                                </div>
                            </div>
                            <div class="form-group">
                                <input type="password" name="new_password" id="new-password" class="form-control" placeholder="新密码" required>
                            </div>
                            <div class="form-group">
                                <button type="submit" id="reset-submit" class="btn-login">
                                    重置密码
                                </button>
                            </div>
                        </form>
                    </div>

                    <div class="back-to-login">
                        <a href="{{ url_for('login') }}">返回登录</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/bootstrap.min.js') }}"></script>
<script>
$(function () {
    // 显示提示消息
    function showAlert(message, isSuccess) {
        var alertBox = $('#alert-message');
        alertBox.removeClass('alert-success alert-error');
        alertBox.addClass(isSuccess ? 'alert-success' : 'alert-error');
        alertBox.text(message).fadeIn();
        setTimeout(function() {
            alertBox.fadeOut();
        }, 3000);
    }

    // 倒计时功能
    function startCountdown(seconds) {
        var countdown = $('#countdown');
        var resendBtn = $('#resend-code');
        resendBtn.hide();
        countdown.text(seconds + '秒后可重新发送').show();

        var timer = setInterval(function() {
            seconds--;
            countdown.text(seconds + '秒后可重新发送');

            if (seconds <= 0) {
                clearInterval(timer);
                countdown.hide();
                resendBtn.show();
            }
        }, 1000);
    }

    // 获取验证码
    $('#request-code-form').on('submit', function(e) {
        e.preventDefault();

        // 获取 CSRF token
        var csrf_token = $('input[name="csrf_token"]').val();

        // 禁用按钮防止重复提交
        var btn = $('#request-code-btn');
        btn.prop('disabled', true).text('发送中...');

        $.ajax({
            url: '/send_captcha',
            type: 'POST',
            contentType: 'application/json',
            headers: {
                'X-CSRFToken': csrf_token
            },
            data: JSON.stringify({
                username: $('#username').val()
            }),
            success: function(response) {
                if(response.code === 200) {
                    // 存储用户名到隐藏字段
                    $('#hidden-username').val($('#username').val());
                    // 切换到验证码输入步骤
                    $('#step1').removeClass('active');
                    $('#step2').addClass('active');
                    // 启动倒计时
                    startCountdown(60);
                    showAlert(response.message, true);
                } else {
                    showAlert(response.message, false);
                }
            },
            error: function(xhr) {
                if(xhr.status === 400) {
                    showAlert(xhr.responseJSON.message || '请求参数错误', false);
                } else {
                    showAlert('请求失败: ' + xhr.statusText, false);
                }
            },
            complete: function() {
                btn.prop('disabled', false).text('获取验证码');
            }
        });
    });

    // 重新发送验证码
    $('#resend-code').on('click', function() {
        var username = $('#hidden-username').val();
        var csrf_token = $('input[name="csrf_token"]').val();

        if (!username) {
            showAlert('请返回第一步重新填写信息', false);
            return;
        }

        $(this).hide();
        $('#countdown').show();
        startCountdown(60);

        $.ajax({
            url: '/send_captcha',
            type: 'POST',
            contentType: 'application/json',
            headers: {
                'X-CSRFToken': csrf_token
            },
            data: JSON.stringify({
                username: username
            }),
            success: function(response) {
                if (response.code === 200) {
                    showAlert('验证码已重新发送', true);
                } else {
                    showAlert(response.message || '验证码发送失败', false);
                }
            },
            error: function(xhr) {
                if(xhr.responseJSON && xhr.responseJSON.message) {
                    showAlert('错误: ' + xhr.responseJSON.message, false);
                } else {
                    showAlert('请求失败，请稍后再试', false);
                }
            }
        });
    });

    // 重置密码
    $('#reset-password-form').on('submit', function(e) {
        e.preventDefault();

        var btn = $('#reset-submit');
        btn.prop('disabled', true).text('处理中...');

        // 获取表单数据
        var formData = {
            username: $('#hidden-username').val(),
            captcha: $('#captcha').val(),
            password: $('#new-password').val(),
            csrf_token: $('input[name="csrf_token"]').val()
        };

        // 验证必填字段
        if (!formData.username || !formData.captcha || !formData.password) {
            showAlert('请填写所有必填字段', false);
            btn.prop('disabled', false).text('重置密码');
            return;
        }

        // 验证密码强度
        if (formData.password.length < 6) {
            showAlert('密码长度至少6位', false);
            btn.prop('disabled', false).text('重置密码');
            return;
        }

        $.ajax({
            url: '/reset_password',
            type: 'POST',
            contentType: 'application/json',
            headers: {
                'X-CSRFToken': formData.csrf_token
            },
            data: JSON.stringify(formData),
            success: function(response) {
                if (response.code === 200) {
                    showAlert('密码重置成功，即将跳转到登录页面', true);
                    setTimeout(function() {
                        window.location.href = "{{ url_for('login') }}";
                    }, 1500);
                } else {
                    showAlert(response.message || '密码重置失败', false);
                }
            },
            error: function(xhr) {
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    showAlert('错误: ' + xhr.responseJSON.message, false);
                } else if (xhr.status === 403) {
                    showAlert('CSRF验证失败，请刷新页面后重试', false);
                } else {
                    showAlert('请求失败，状态码: ' + xhr.status, false);
                }
            },
            complete: function() {
                btn.prop('disabled', false).text('重置密码');
            }
        });
    });
});
</script>
</body>
</html>