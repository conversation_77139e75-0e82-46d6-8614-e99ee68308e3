<!DOCTYPE html>
<html lang="en">

<head>

    <title>新能源汽车数据分析系统</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="description" content=""/>
    <meta name="keywords" content="">
    <meta name="author" content="Phoenixcoded"/>
    <link rel="icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>

<div class="auth-wrapper">
    <div class="auth-content">
        <div class="card">
            <div class="row align-items-center text-center">
                <div class="col-md-12">
                    <div class="card-body">
                        <img src="{{ url_for('static',filename='images/logo-dark.png') }}" alt="" class="img-fluid mb-4">
                        <h4 class="mb-3 f-w-400">重置密码</h4>

                        <!-- 提示消息 -->
                        <div id="alert-message" style="display: none;" class="alert mb-3"></div>

                        <!-- 第一步：输入用户名 -->
                        <div id="step1" class="step">
                            <div class="form-group mb-3">
                                <label class="floating-label" for="username">用户名</label>
                                <input type="text" class="form-control" id="username" placeholder="">
                            </div>
                            <button class="btn btn-block btn-primary mb-4" id="request-code-btn">获取验证码</button>
                        </div>

                        <!-- 第二步：输入验证码和新密码 -->
                        <div id="step2" class="step" style="display: none;">
                            <div class="form-group mb-3">
                                <label class="floating-label" for="captcha">验证码</label>
                                <input type="text" class="form-control" id="captcha" placeholder="">
                                <div class="text-right mt-2">
                                    <small class="text-muted" id="countdown" style="display: none;">60秒后可重新发送</small>
                                    <a href="#" id="resend-code" class="f-w-400" style="display: none;">重新发送验证码</a>
                                </div>
                            </div>
                            <div class="form-group mb-4">
                                <label class="floating-label" for="new-password">新密码</label>
                                <input type="password" class="form-control" id="new-password" placeholder="">
                            </div>
                            <button class="btn btn-block btn-primary mb-4" id="reset-submit">重置密码</button>
                        </div>

                        <p class="mb-0 text-muted">返回 <a href="{{ url_for('login') }}" class="f-w-400">登录页面</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="{{ url_for('static', filename='js/vendor-all.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/plugins/bootstrap.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/ripple.js') }}"></script>
<script src="{{ url_for('static', filename='js/pcoded.min.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const requestCodeBtn = document.getElementById('request-code-btn');
    const resetSubmitBtn = document.getElementById('reset-submit');
    const resendCodeBtn = document.getElementById('resend-code');
    const usernameInput = document.getElementById('username');
    const captchaInput = document.getElementById('captcha');
    const newPasswordInput = document.getElementById('new-password');
    const step1 = document.getElementById('step1');
    const step2 = document.getElementById('step2');
    const alertMessage = document.getElementById('alert-message');
    const countdown = document.getElementById('countdown');

    let currentUsername = '';

    // 显示提示消息
    function showAlert(message, isSuccess) {
        alertMessage.className = 'alert mb-3 ' + (isSuccess ? 'alert-success' : 'alert-danger');
        alertMessage.textContent = message;
        alertMessage.style.display = 'block';
        setTimeout(function() {
            alertMessage.style.display = 'none';
        }, 3000);
    }

    // 倒计时功能
    function startCountdown(seconds) {
        resendCodeBtn.style.display = 'none';
        countdown.textContent = seconds + '秒后可重新发送';
        countdown.style.display = 'inline';

        const timer = setInterval(function() {
            seconds--;
            countdown.textContent = seconds + '秒后可重新发送';

            if (seconds <= 0) {
                clearInterval(timer);
                countdown.style.display = 'none';
                resendCodeBtn.style.display = 'inline';
            }
        }, 1000);
    }

    // 获取验证码
    requestCodeBtn.addEventListener('click', function(e) {
        e.preventDefault();

        const username = usernameInput.value.trim();

        if (!username) {
            showAlert('请输入用户名', false);
            usernameInput.focus();
            return;
        }

        // 禁用按钮防止重复提交
        requestCodeBtn.disabled = true;
        requestCodeBtn.textContent = '发送中...';

        // 发送获取验证码请求
        fetch('/send_captcha', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                currentUsername = username;
                // 切换到第二步
                step1.style.display = 'none';
                step2.style.display = 'block';
                // 启动倒计时
                startCountdown(60);
                showAlert(data.message || '验证码已发送', true);
            } else {
                showAlert(data.message || '发送验证码失败', false);
            }
        })
        .catch(error => {
            console.error('发送验证码失败:', error);
            showAlert('网络错误，请稍后重试', false);
        })
        .finally(() => {
            // 恢复按钮状态
            requestCodeBtn.disabled = false;
            requestCodeBtn.textContent = '获取验证码';
        });
    });

    // 重新发送验证码
    resendCodeBtn.addEventListener('click', function(e) {
        e.preventDefault();

        if (!currentUsername) {
            showAlert('请返回第一步重新填写信息', false);
            return;
        }

        // 重新启动倒计时
        startCountdown(60);

        // 发送重新获取验证码请求
        fetch('/send_captcha', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: currentUsername
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                showAlert('验证码已重新发送', true);
            } else {
                showAlert(data.message || '验证码发送失败', false);
            }
        })
        .catch(error => {
            console.error('重新发送验证码失败:', error);
            showAlert('网络错误，请稍后重试', false);
        });
    });

    // 重置密码
    resetSubmitBtn.addEventListener('click', function(e) {
        e.preventDefault();

        const captcha = captchaInput.value.trim();
        const newPassword = newPasswordInput.value.trim();

        // 基本验证
        if (!captcha) {
            showAlert('请输入验证码', false);
            captchaInput.focus();
            return;
        }

        if (!newPassword) {
            showAlert('请输入新密码', false);
            newPasswordInput.focus();
            return;
        }

        if (newPassword.length < 6) {
            showAlert('密码长度至少6位', false);
            newPasswordInput.focus();
            return;
        }

        // 禁用按钮防止重复提交
        resetSubmitBtn.disabled = true;
        resetSubmitBtn.textContent = '处理中...';

        // 发送重置密码请求
        fetch('/reset_password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: currentUsername,
                captcha: captcha,
                password: newPassword
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                showAlert('密码重置成功，即将跳转到登录页面', true);
                setTimeout(function() {
                    window.location.href = '/login';
                }, 1500);
            } else {
                showAlert(data.message || '密码重置失败', false);
            }
        })
        .catch(error => {
            console.error('重置密码失败:', error);
            showAlert('网络错误，请稍后重试', false);
        })
        .finally(() => {
            // 恢复按钮状态
            resetSubmitBtn.disabled = false;
            resetSubmitBtn.textContent = '重置密码';
        });
    });

    // 回车键支持
    usernameInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            requestCodeBtn.click();
        }
    });

    [captchaInput, newPasswordInput].forEach(input => {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                resetSubmitBtn.click();
            }
        });
    });
});
</script>
</body>
</html>