-- 创建车辆数据表（如果不存在）
CREATE TABLE IF NOT EXISTS vehicle_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    vehicleid VARCHAR(50),
    carname VARCHAR(100),
    image TEXT,
    rank INT,
    lowestprice DECIMAL(10,2),
    highestprice DECIMAL(10,2),
    lastmonthrank INT,
    sales INT,
    rating DECIMAL(3,1),
    commentcount INT,
    pricedrop DECIMAL(10,2),
    relatedimages INT,
    brandid INT,
    detailtype VARCHAR(100),
    brandname VARCHAR(100),
    subbrandid INT,
    subbrandname VARCHAR(100),
    pricerange VARCHAR(50),
    dealerprice VARCHAR(50),
    time VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建用户收藏表
CREATE TABLE IF NOT EXISTS user_favorites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    vehicle_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (user_id) REFERENCES user_info(id) ON DELETE CASCADE,
    FOREIGN KEY (vehicle_id) REFERENCES vehicle_data(id) ON DELETE CASCADE,
    UNIQUE KEY unique_favorite (user_id, vehicle_id)
);

-- 创建车辆评论表
CREATE TABLE IF NOT EXISTS vehicle_comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    vehicle_id INT NOT NULL,
    content TEXT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user_info(id) ON DELETE CASCADE,
    FOREIGN KEY (vehicle_id) REFERENCES vehicle_data(id) ON DELETE CASCADE
);

-- 插入示例车辆数据（基于您提供的字段结构）
INSERT INTO vehicle_data (
    vehicleid, carname, image, rank, lowestprice, highestprice, lastmonthrank, 
    sales, rating, commentcount, pricedrop, relatedimages, brandid, detailtype, 
    brandname, subbrandid, subbrandname, pricerange, dealerprice, time
) VALUES 
(6148, '熊猫', 'https://p3-dcd.byteimg.com/img/tos-cn-i-dcdx/faf3420cf3fd4f949f777c2ad1d1d0cc~tplv-resize:640:0.png', 
 12, 2.99, 5.69, 0, 6661, 0, 168, 0, 4926, 73, 0, '吉利汽车', 86, '吉利汽车', '2.99-5.69万', '2.99-5.69万', '202301'),

(6149, '比亚迪秦PLUS', 'https://example.com/qin-plus.jpg', 
 5, 10.58, 14.58, 6, 15230, 4.5, 892, 0.5, 3245, 12, 1, '比亚迪', 12, '比亚迪', '10.58-14.58万', '10.58-14.58万', '202301'),

(6150, '特斯拉Model 3', 'https://example.com/model3.jpg', 
 3, 26.59, 34.99, 2, 8956, 4.8, 1245, 2.0, 5678, 45, 2, '特斯拉', 45, '特斯拉', '26.59-34.99万', '26.59-34.99万', '202301'),

(6151, '蔚来ES6', 'https://example.com/es6.jpg', 
 8, 35.80, 52.60, 10, 4567, 4.6, 567, 1.2, 2890, 78, 3, '蔚来', 78, '蔚来', '35.80-52.60万', '35.80-52.60万', '202301'),

(6152, '小鹏P7', 'https://example.com/p7.jpg', 
 15, 22.99, 40.99, 12, 3456, 4.4, 423, 0.8, 1987, 89, 4, '小鹏汽车', 89, '小鹏汽车', '22.99-40.99万', '22.99-40.99万', '202301'),

(6153, '理想ONE', 'https://example.com/one.jpg', 
 7, 33.80, 33.80, 8, 5234, 4.7, 678, 0, 3456, 56, 5, '理想汽车', 56, '理想汽车', '33.80万', '33.80万', '202301'),

(6154, '欧拉好猫', 'https://example.com/haomao.jpg', 
 20, 10.39, 14.39, 18, 2345, 4.2, 234, 0.3, 1567, 34, 6, '欧拉', 34, '长城汽车', '10.39-14.39万', '10.39-14.39万', '202301'),

(6155, '五菱宏光MINI EV', 'https://example.com/mini-ev.jpg', 
 1, 2.88, 4.86, 1, 25678, 4.0, 1567, 0, 2345, 23, 7, '五菱', 23, '上汽通用五菱', '2.88-4.86万', '2.88-4.86万', '202301'),

(6156, '广汽埃安AION S', 'https://example.com/aion-s.jpg', 
 11, 13.98, 20.58, 9, 4789, 4.3, 456, 0.6, 2134, 67, 8, '广汽埃安', 67, '广汽埃安', '13.98-20.58万', '13.98-20.58万', '202301'),

(6157, '哪吒V', 'https://example.com/nezha-v.jpg', 
 25, 5.99, 12.08, 22, 1890, 3.9, 189, 0.2, 987, 45, 9, '哪吒汽车', 45, '哪吒汽车', '5.99-12.08万', '5.99-12.08万', '202301');

-- 创建索引以提高查询性能
CREATE INDEX idx_vehicle_brand ON vehicle_data(brandname);
CREATE INDEX idx_vehicle_price ON vehicle_data(lowestprice, highestprice);
CREATE INDEX idx_vehicle_time ON vehicle_data(time);
CREATE INDEX idx_vehicle_rank ON vehicle_data(rank);
CREATE INDEX idx_vehicle_sales ON vehicle_data(sales);

CREATE INDEX idx_favorites_user ON user_favorites(user_id);
CREATE INDEX idx_favorites_vehicle ON user_favorites(vehicle_id);

CREATE INDEX idx_comments_vehicle ON vehicle_comments(vehicle_id);
CREATE INDEX idx_comments_user ON vehicle_comments(user_id);
CREATE INDEX idx_comments_created ON vehicle_comments(created_at);
