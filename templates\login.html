<!DOCTYPE html>
<html lang="en">

<head>

    <title>新能源汽车数据分析系统</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="description" content=""/>
    <meta name="keywords" content="">
    <meta name="author" content="Phoenixcoded"/>
    <link rel="icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>


<div class="auth-wrapper">
    <div class="auth-content">
        <div class="card">
            <div class="row align-items-center text-center">
                <div class="col-md-12">
                    <div class="card-body">
                        <img src="{{ url_for('static',filename='images/logo-dark.png') }}" alt="" class="img-fluid mb-4">
                        <h4 class="mb-3 f-w-400">用户登录</h4>
                        <div class="form-group mb-3">
                            <label class="floating-label" for="username">用户名</label>
                            <input type="text" class="form-control" id="username" placeholder="">
                        </div>
                        <div class="form-group mb-4">
                            <label class="floating-label" for="Password">密码</label>
                            <input type="password" class="form-control" id="password" placeholder="">
                        </div>
                        <div class="custom-control custom-checkbox text-left mb-4 mt-2">
                            <input type="checkbox" class="custom-control-input" id="customCheck1">
                            <label class="custom-control-label" for="customCheck1">记住我.</label>
                        </div>
                        <button class="btn btn-block btn-primary mb-4" id="loginBtn">登录</button>
                        <p class="mb-2 text-muted">忘记密码? <a href="auth-reset-password.html" class="f-w-400">重置密码</a>
                        </p>
                        <p class="mb-0 text-muted">还没有账号? <a href="{{ url_for('register') }}"
                                                                             class="f-w-400">去注册</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="{{ url_for('static', filename='js/vendor-all.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/plugins/bootstrap.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/ripple.js') }}"></script>
<script src="{{ url_for('static', filename='js/pcoded.min.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginBtn = document.getElementById('loginBtn');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');

    loginBtn.addEventListener('click', function(e) {
        e.preventDefault();

        const username = usernameInput.value.trim();
        const password = passwordInput.value.trim();

        // 基本验证
        if (!username) {
            alert('请输入用户名');
            usernameInput.focus();
            return;
        }

        if (!password) {
            alert('请输入密码');
            passwordInput.focus();
            return;
        }

        // 禁用按钮防止重复提交
        loginBtn.disabled = true;
        loginBtn.textContent = '登录中...';

        // 创建表单数据
        const formData = new FormData();
        formData.append('username', username);
        formData.append('password', password);

        // 发送登录请求
        fetch('/login', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                alert('登录成功！');
                // 登录成功后可以跳转到主页或其他页面
                // window.location.href = '/dashboard'; // 根据需要修改跳转地址
            } else {
                alert(data.message || '登录失败');
            }
        })
        .catch(error => {
            console.error('登录请求失败:', error);
            alert('网络错误，请稍后重试');
        })
        .finally(() => {
            // 恢复按钮状态
            loginBtn.disabled = false;
            loginBtn.textContent = '登录';
        });
    });

    // 回车键登录
    [usernameInput, passwordInput].forEach(input => {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                loginBtn.click();
            }
        });
    });
});
</script>

</body>

</html>
