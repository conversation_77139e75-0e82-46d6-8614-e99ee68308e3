import pymysql
import pandas as pd
import numpy as np
from config import Config

def coon():
    con = pymysql.connect(host=Config.Host, port=Config.Port, user=Config.User, password=Config.Password, db=Config.Database,
                          charset='utf8')  # 连接数据库
    cur = con.cursor()
    return con, cur


def close():
    con, cur = coon()  # 关闭数据库
    cur.close()
    con.close()


def qurey(sql):
    con, cur = coon()  # 查询数据库
    cur.execute(sql)
    res = cur.fetchall()
    close()
    return res


def insert(sql):
    con, cur = coon()  # 删除、修改数据库表
    cur.execute(sql)
    con.commit()
    close()



